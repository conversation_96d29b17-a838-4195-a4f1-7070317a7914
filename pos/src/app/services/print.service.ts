import { Injectable } from '@angular/core';
import { PrintTemplateUtil } from '../utils/print-template.util';
import { CartCalculationUtils } from '../utils/cart-calculation.utils';
import { PrintTemplateData, PrintableItem } from '../models/print.model';
import { Order, CartItem } from '../models';
import { TypeSenseService } from './typesense';

interface StoreInfo {
  address: string;
  phone: string;
  gstin: string;
  name: string;
  email: string;
}

@Injectable({
  providedIn: 'root'
})
export class PrintService {
 static DEFAULT_STORE_INFO: StoreInfo = {
    address: 'New Delhi, India',
    phone: '9667018020',
    gstin: '27AAPC1234D1Z1',
    name: 'ROZANA RURAL COMMERCE PVT LTD',
    email: '<EMAIL>'
  };

  private storeInfoCache: StoreInfo | null = null;

  constructor(private typeSenseService: TypeSenseService) { }

  clearStoreCache(): void {
    this.storeInfoCache = null;
  }

  private async getStoreInfo(): Promise<StoreInfo> {
    if (this.storeInfoCache) return this.storeInfoCache;

    try {
      const storeData = await this.typeSenseService.getStoreById();
      return this.storeInfoCache = { ...PrintService.DEFAULT_STORE_INFO, ...storeData };
    } catch (error) {
      console.error('Error fetching store info, using defaults:', error);
      return this.storeInfoCache = { ...PrintService.DEFAULT_STORE_INFO };
    }
  }



  private convertToItem(item: CartItem | any, isOrder = false): PrintableItem {
    const mrp = item.mrp || item.unit_price || item.selling_price || item.sale_price;
    const salePrice = item.selling_price || item.sale_price;
    const igstRate = item.igst || item.tax || 0;
    const cessRate = item.cess || 0;

    return {
      sku: item.child_sku || item.sku,
      name: item.name,
      quantity: item.quantity,
      unit_price: mrp,
      sale_price: salePrice,
      mrp,
      discount: Math.max(0, mrp - salePrice),
      total: salePrice * item.quantity,
      cgst: isOrder ? 0 : item.cgst,
      sgst: isOrder ? 0 : item.sgst,
      igst: igstRate,
      cess: cessRate,
      tax: item.tax || 0
    };
  }

  private async generateTemplate(items: PrintableItem[], orderData: any): Promise<string> {
    const totals = CartCalculationUtils.calculatePrintableTotals(items);
    const storeInfo = await this.getStoreInfo();
    const calculatedTotal = totals.totalTaxableAmount + totals.totalTax;

    const templateData: PrintTemplateData = {
      order_id: orderData.orderId || orderData.order_id,
      customer_name: orderData.customerName || orderData.customer_name || '',
      customer_id: orderData.customerId || orderData.customer_id || '',
      facility_name: orderData.facilityName || orderData.facility_name || storeInfo.name,
      total_amount: orderData.totalAmount || orderData.total_amount || calculatedTotal,
      items,
      payment_method: orderData.paymentMethod || orderData.payment?.payment_mode || 'Cash',
      subtotal: orderData.subtotalAmount || orderData.subtotal_amount || totals.totalTaxableAmount,
      discount: orderData.discountAmount || orderData.discount_amount || items.reduce((total, item) => total + (item.discount || 0) * item.quantity, 0),
      grand_total: orderData.totalAmount || orderData.total_amount || calculatedTotal,
      copy_of_invoice: orderData.copyOfInvoice || false,
      currentDate: (orderData.orderDate ? new Date(orderData.orderDate) : orderData.order_date ? new Date(orderData.order_date) : new Date()).toLocaleDateString('en-IN'),
      totals,
      storeAddress: storeInfo.address,
      storePhone: storeInfo.phone,
      storeGSTIN: storeInfo.gstin,
      storeEmail: storeInfo.email,
      created_at: orderData.createdAt || orderData.created_at?.toString()
    };

    return PrintTemplateUtil.generateTemplate(templateData);
  }

  async generateCartPrintTemplate(cartItems: CartItem[], orderId: string, customerName = '', paymentMethod = 'Cash', copyOfInvoice = false): Promise<string> {
    const items = cartItems.map(item => this.convertToItem(item));
    return this.generateTemplate(items, { orderId, customerName, paymentMethod, copyOfInvoice });
  }

  async generateOrderPrintTemplate(order: Order, copyOfInvoice = false): Promise<string> {
    const items = order.items.map(item => this.convertToItem(item, true));
    return this.generateTemplate(items, { ...order, copyOfInvoice });
  }

  private printTemplate(htmlTemplate: string): void {
    try {
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        console.error('Failed to open print window. Please check popup blocker settings.');
        return;
      }

      printWindow.document.open();
      printWindow.document.write(htmlTemplate);
      printWindow.document.close();
      printWindow.focus();
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    } catch (error) {
      console.error('Error printing template:', error);
    }
  }

  async printCart(cartItems: CartItem[], orderId: string, customerName = '', paymentMethod = 'Cash', copyOfInvoice = false): Promise<void> {
    const template = await this.generateCartPrintTemplate(cartItems, orderId, customerName, paymentMethod, copyOfInvoice);
    this.printTemplate(template);
  }

  async printOrder(order: Order, copyOfInvoice = false): Promise<void> {
    const template = await this.generateOrderPrintTemplate(order, copyOfInvoice);
    this.printTemplate(template);
  }
}